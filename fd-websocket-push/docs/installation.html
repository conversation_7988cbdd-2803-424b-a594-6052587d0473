<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安装配置 - FD WebSocket Push 插件文档</title>
    <link rel="stylesheet" href="assets/style.css">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1 class="logo">
                    <span class="logo-icon">🚀</span>
                    FD WebSocket Push
                </h1>
                <p class="subtitle">安装配置指南</p>
            </div>
        </header>

        <nav class="navigation">
            <ul class="nav-list">
                <li><a href="index.html" class="nav-link">概述</a></li>
                <li><a href="installation.html" class="nav-link active">安装配置</a></li>
                <li><a href="api-reference.html" class="nav-link">API 参考</a></li>
                <li><a href="events.html" class="nav-link">事件系统</a></li>
                <li><a href="cache.html" class="nav-link">缓存管理</a></li>
                <li><a href="migration.html" class="nav-link">迁移指南</a></li>
                <li><a href="troubleshooting.html" class="nav-link">故障排除</a></li>
                <li><a href="examples.html" class="nav-link">使用示例</a></li>
            </ul>
        </nav>

        <main class="main-content">
            <section class="content-section">
                <h2>安装配置</h2>

                <div class="info-section">
                    <h3>系统要求</h3>
                    <table class="info-table">
                        <tr>
                            <td><strong>WordPress 版本</strong></td>
                            <td>5.0 或更高版本</td>
                        </tr>
                        <tr>
                            <td><strong>PHP 版本</strong></td>
                            <td>7.4 或更高版本</td>
                        </tr>
                        <tr>
                            <td><strong>内存要求</strong></td>
                            <td>最少 128MB（推荐 256MB）</td>
                        </tr>
                        <tr>
                            <td><strong>必需扩展</strong></td>
                            <td>curl, json</td>
                        </tr>
                        <tr>
                            <td><strong>可选扩展</strong></td>
                            <td>ACF (Advanced Custom Fields)</td>
                        </tr>
                    </table>
                </div>

                <div class="info-section">
                    <h3>步骤 1: 下载和上传插件</h3>
                    
                    <h4>方法一：手动上传</h4>
                    <ol class="quick-start-list">
                        <li>
                            <strong>下载插件文件</strong>
                            <p>从 GitHub 或其他来源下载 <code>fd-websocket-push</code> 插件文件夹</p>
                        </li>
                        <li>
                            <strong>上传到服务器</strong>
                            <p>将整个 <code>fd-websocket-push</code> 文件夹上传到 <code>/wp-content/plugins/</code> 目录</p>
                        </li>
                        <li>
                            <strong>验证文件结构</strong>
                            <p>确保文件结构如下：</p>
                            <pre><code>/wp-content/plugins/fd-websocket-push/
├── fd-websocket-push.php
├── includes/
│   ├── class-helper.php
│   ├── class-cache-invalidator.php
│   ├── class-websocket-pusher.php
│   ├── class-post-event-handler.php
│   ├── class-taxonomy-event-handler.php
│   └── class-payment-event-handler.php
└── README.md</code></pre>
                        </li>
                    </ol>

                    <h4>方法二：通过 WordPress 后台上传</h4>
                    <ol class="quick-start-list">
                        <li>
                            <strong>打包插件</strong>
                            <p>将 <code>fd-websocket-push</code> 文件夹压缩为 ZIP 文件</p>
                        </li>
                        <li>
                            <strong>上传插件</strong>
                            <p>在 WordPress 后台 → 插件 → 安装插件 → 上传插件，选择 ZIP 文件上传</p>
                        </li>
                        <li>
                            <strong>安装插件</strong>
                            <p>点击"现在安装"按钮完成安装</p>
                        </li>
                    </ol>
                </div>

                <div class="info-section">
                    <h3>步骤 2: 配置必需常量</h3>
                    
                    <p>在 <code>wp-config.php</code> 文件中添加以下常量定义：</p>
                    
                    <pre><code>// WebSocket 推送密钥
define( 'FD_WEBSOCKET_PUSH_SECRET', 'your-secure-websocket-secret-key' );

// Next.js 缓存失效密钥
define( 'REVALIDATE_SECRET', 'your-secure-revalidate-secret-key' );</code></pre>

                    <div class="warning-box">
                        <h4>⚠️ 安全提示</h4>
                        <ul>
                            <li>使用强密码生成器生成安全的密钥</li>
                            <li>密钥长度建议至少 32 个字符</li>
                            <li>不要在代码中硬编码密钥</li>
                            <li>定期更换密钥以提高安全性</li>
                        </ul>
                    </div>

                    <h4>密钥生成示例</h4>
                    <p>您可以使用以下方法生成安全的密钥：</p>
                    
                    <pre><code># 使用 OpenSSL 生成随机密钥
openssl rand -base64 32

# 使用 WordPress 密钥生成器
# 访问：https://api.wordpress.org/secret-key/1.1/salt/

# PHP 生成示例
bin2hex(random_bytes(32))</code></pre>
                </div>

                <div class="info-section">
                    <h3>步骤 3: 激活插件</h3>
                    
                    <ol class="quick-start-list">
                        <li>
                            <strong>进入插件管理页面</strong>
                            <p>在 WordPress 后台导航到 插件 → 已安装的插件</p>
                        </li>
                        <li>
                            <strong>找到插件</strong>
                            <p>在插件列表中找到 "FD WebSocket Push"</p>
                        </li>
                        <li>
                            <strong>激活插件</strong>
                            <p>点击"激活"按钮启用插件</p>
                        </li>
                        <li>
                            <strong>验证激活</strong>
                            <p>激活成功后，插件状态应显示为"已激活"</p>
                        </li>
                    </ol>
                </div>

                <div class="info-section">
                    <h3>步骤 4: 验证安装</h3>
                    
                    <h4>检查错误日志</h4>
                    <p>插件激活后，检查 WordPress 错误日志中是否有相关消息：</p>
                    
                    <pre><code># 成功激活的日志示例
[FD WebSocket Push] FD WebSocket Push plugin activated

# 配置错误的日志示例
[FD WebSocket Push ERROR] FD_WEBSOCKET_PUSH_SECRET not defined
[FD WebSocket Push ERROR] REVALIDATE_SECRET not defined</code></pre>

                    <h4>测试基本功能</h4>
                    <ol class="quick-start-list">
                        <li>
                            <strong>创建测试文章</strong>
                            <p>创建一篇新文章并发布，检查日志中是否有推送事件</p>
                        </li>
                        <li>
                            <strong>更新文章</strong>
                            <p>编辑并保存文章，验证更新事件是否触发</p>
                        </li>
                        <li>
                            <strong>检查 WebSocket 连接</strong>
                            <p>确认 WebSocket 服务器能够接收到推送事件</p>
                        </li>
                    </ol>
                </div>

                <div class="info-section">
                    <h3>高级配置选项</h3>
                    
                    <h4>自定义 WebSocket 服务器地址</h4>
                    <p>如果您的 WebSocket 服务器不在默认地址，可以通过过滤器自定义：</p>
                    
                    <pre><code>// 在主题的 functions.php 中添加
add_filter( 'fd_websocket_push_server_url', function( $url ) {
    return 'http://your-websocket-server:8080/push-event';
} );</code></pre>

                    <h4>自定义前端服务器地址</h4>
                    <p>如果您的 Next.js 前端服务器不在默认地址：</p>
                    
                    <pre><code>// 自定义前端服务器地址
add_filter( 'fd_websocket_push_frontend_url', function( $url ) {
    return 'http://your-frontend-server:3000';
} );</code></pre>

                    <h4>禁用特定事件类型</h4>
                    <p>您可以选择性地禁用某些事件类型：</p>
                    
                    <pre><code>// 禁用文章插入事件
add_filter( 'fd_websocket_push_enable_post_insert', '__return_false' );

// 禁用分类法事件
add_filter( 'fd_websocket_push_enable_taxonomy_events', '__return_false' );</code></pre>
                </div>

                <div class="info-section">
                    <h3>性能优化建议</h3>
                    
                    <ul class="feature-list">
                        <li><strong>对象缓存</strong> - 启用 Redis 或 Memcached 对象缓存以提高性能</li>
                        <li><strong>日志管理</strong> - 定期清理 WordPress 错误日志文件</li>
                        <li><strong>网络优化</strong> - 确保 WebSocket 服务器与 WordPress 之间的网络延迟较低</li>
                        <li><strong>资源监控</strong> - 监控服务器资源使用情况，特别是内存和 CPU</li>
                        <li><strong>批量操作</strong> - 避免在短时间内进行大量文章操作</li>
                    </ul>
                </div>

                <div class="warning-box">
                    <h4>🔧 故障排除快速检查</h4>
                    <ul>
                        <li>确认 PHP 版本符合要求（7.4+）</li>
                        <li>检查 WordPress 错误日志</li>
                        <li>验证常量是否正确定义</li>
                        <li>确认 WebSocket 服务器正在运行</li>
                        <li>检查网络连接和防火墙设置</li>
                    </ul>
                </div>
            </section>
        </main>

        <footer class="footer">
            <div class="footer-content">
                <p>&copy; 2024 FD WebSocket Push Plugin. 保留所有权利。</p>
                <p>
                    <a href="https://github.com/your-repo/fd-websocket-push" target="_blank">GitHub</a> |
                    <a href="mailto:<EMAIL>">技术支持</a> |
                    <a href="troubleshooting.html">故障排除</a>
                </p>
            </div>
        </footer>
    </div>

    <script src="assets/script.js"></script>
</body>
</html>
