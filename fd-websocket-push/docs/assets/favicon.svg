<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#gradient)" stroke="#1e293b" stroke-width="1"/>
  
  <!-- WebSocket icon (simplified) -->
  <g fill="white" stroke="white" stroke-width="0.5">
    <!-- Signal waves -->
    <path d="M8 16 Q12 12, 16 16 Q20 20, 24 16" fill="none" stroke-width="1.5"/>
    <path d="M10 16 Q13 14, 16 16 Q19 18, 22 16" fill="none" stroke-width="1"/>
    <path d="M12 16 Q14 15, 16 16 Q18 17, 20 16" fill="none" stroke-width="0.8"/>
    
    <!-- Central dot -->
    <circle cx="16" cy="16" r="1.5" fill="white"/>
  </g>
  
  <!-- Push notification indicator -->
  <circle cx="24" cy="8" r="3" fill="#f59e0b" stroke="white" stroke-width="1"/>
  <text x="24" y="9.5" text-anchor="middle" font-family="Arial, sans-serif" font-size="3" fill="white" font-weight="bold">!</text>
</svg>
