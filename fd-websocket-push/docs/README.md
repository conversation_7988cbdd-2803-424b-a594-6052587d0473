# FD WebSocket Push 插件文档

欢迎使用 FD WebSocket Push 插件的完整文档系统！

## 📚 文档导航

### 🚀 快速开始
- **[插件概述](index.html)** - 了解插件功能和特性
- **[安装配置](installation.html)** - 详细的安装和配置指南
- **[迁移指南](migration.html)** - 从 fd-pusher 迁移到 fd-websocket-push

### 📖 深入了解
- **[API 参考](api-reference.html)** - 完整的 API 文档和类方法说明
- **[事件系统](events.html)** - WebSocket 事件类型和数据结构详解
- **[缓存管理](cache.html)** - Next.js 缓存失效机制和配置

### 🛠️ 实用指南
- **[使用示例](examples.html)** - 实际使用场景和代码示例
- **[故障排除](troubleshooting.html)** - 常见问题解决方案
- **[更新日志](changelog.html)** - 版本更新记录和变更说明

## 🎯 文档特性

- **📱 响应式设计** - 支持桌面和移动设备
- **🔍 搜索功能** - 快速查找相关内容
- **🌙 深色模式** - 支持明暗主题切换
- **📋 代码复制** - 一键复制代码示例
- **🔗 交叉引用** - 文档间的便捷导航

## 🚀 在线访问

如果您正在查看本地文件，建议通过 Web 服务器访问以获得最佳体验：

```bash
# 使用 Python 启动本地服务器
cd fd-websocket-push/docs
python -m http.server 8000

# 或使用 Node.js
npx http-server -p 8000

# 然后访问 http://localhost:8000
```

## 📝 文档贡献

如果您发现文档中的错误或希望改进内容，欢迎：

1. 在 [GitHub Issues](https://github.com/your-repo/fd-websocket-push/issues) 提交问题
2. 提交 Pull Request 改进文档
3. 发送邮件到 [<EMAIL>](mailto:<EMAIL>)

## 📄 许可证

本文档采用 [MIT License](https://opensource.org/licenses/MIT) 许可证。

---

**开始探索：** [插件概述](index.html) | [安装配置](installation.html) | [API 参考](api-reference.html)
