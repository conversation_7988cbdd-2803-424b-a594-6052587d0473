<?php
/**
 * Post event handler for FD WebSocket Push plugin
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class FD_WebSocket_Push_Post_Event_Handler {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * WebSocket pusher instance
     */
    private $websocket_pusher;
    
    /**
     * Cache invalidator instance
     */
    private $cache_invalidator;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->websocket_pusher = FD_WebSocket_Push_WebSocket_Pusher::get_instance();
        $this->cache_invalidator = FD_WebSocket_Push_Cache_Invalidator::get_instance();
        
        $this->register_hooks();
    }
    
    /**
     * Register WordPress hooks
     */
    private function register_hooks() {
        // Post lifecycle events
        add_action( 'save_post', array( $this, 'handle_post_update' ), 10, 3 );
        add_action( 'wp_insert_post', array( $this, 'handle_post_insert' ), 10, 3 );
        add_action( 'before_delete_post', array( $this, 'handle_post_delete' ), 10, 2 );
        add_action( 'transition_post_status', array( $this, 'handle_post_status_transition' ), 10, 3 );
    }
    
    /**
     * Handle post update event
     *
     * @param int $post_id
     * @param WP_Post $post
     * @param bool $update
     */
    public function handle_post_update( $post_id, $post, $update ) {
        // Only trigger on actual updates, not new posts
        if ( ! $update ) {
            return;
        }
        
        FD_WebSocket_Push_Helper::log( 'Processing post update for post ' . $post_id . ' (type: ' . $post->post_type . ', status: ' . $post->post_status . ')' );
        
        // Send WebSocket events
        $this->websocket_pusher->send_post_updated_event( $post_id, $post );
        
        // Send list update event for published posts
        if ( $post->post_status === 'publish' ) {
            $this->websocket_pusher->send_post_updated_for_lists_event( $post_id, $post );
        }
        
        // Invalidate caches
        $this->cache_invalidator->invalidate_post_caches( $post_id, $post );
        $this->cache_invalidator->invalidate_list_caches_on_post_update( $post_id, $post );
    }
    
    /**
     * Handle post insert event (new posts)
     *
     * @param int $post_id
     * @param WP_Post $post
     * @param bool $update
     */
    public function handle_post_insert( $post_id, $post, $update ) {
        // Only handle new posts (not updates)
        if ( $update ) {
            FD_WebSocket_Push_Helper::log( 'Skipping post insert handler because this is an update, not a new post' );
            return;
        }
        
        // Only handle published posts
        if ( $post->post_status !== 'publish' ) {
            FD_WebSocket_Push_Helper::log( 'Skipping post insert handler because post status is not publish: ' . $post->post_status );
            return;
        }
        
        // Check if post type is public
        if ( ! FD_WebSocket_Push_Helper::is_public_post_type( $post->post_type ) ) {
            FD_WebSocket_Push_Helper::log( 'Skipping post insert handler because post type "' . $post->post_type . '" is not public' );
            return;
        }
        
        FD_WebSocket_Push_Helper::log( 'Processing post insert for post ' . $post_id . ' (type: ' . $post->post_type . ', title: ' . $post->post_title . ')' );
        
        // Send WebSocket event
        $this->websocket_pusher->send_post_inserted_event( $post_id, $post );
        
        // Invalidate caches
        $this->cache_invalidator->invalidate_caches_on_post_insert( $post_id, $post );
    }
    
    /**
     * Handle post delete event
     *
     * @param int $post_id
     * @param WP_Post $post
     */
    public function handle_post_delete( $post_id, $post ) {
        // Only handle published posts (since only they appear in lists)
        if ( $post->post_status !== 'publish' ) {
            FD_WebSocket_Push_Helper::log( 'Skipping post delete handler because post status is not publish: ' . $post->post_status );
            return;
        }
        
        // Check if post type is public
        if ( ! FD_WebSocket_Push_Helper::is_public_post_type( $post->post_type ) ) {
            FD_WebSocket_Push_Helper::log( 'Skipping post delete handler because post type "' . $post->post_type . '" is not public' );
            return;
        }
        
        FD_WebSocket_Push_Helper::log( 'Processing post delete for post ' . $post_id . ' (type: ' . $post->post_type . ', title: ' . $post->post_title . ')' );
        
        // Send WebSocket event
        $this->websocket_pusher->send_post_deleted_event( $post_id, $post );
        
        // Invalidate caches
        $this->cache_invalidator->invalidate_caches_on_post_delete( $post_id, $post );
    }
    
    /**
     * Handle post status transition event
     *
     * @param string $new_status
     * @param string $old_status
     * @param WP_Post $post
     */
    public function handle_post_status_transition( $new_status, $old_status, $post ) {
        FD_WebSocket_Push_Helper::log( 'Post status transition for post ' . $post->ID . ' (old: ' . $old_status . ' -> new: ' . $new_status . ', type: ' . $post->post_type . ')' );
        
        // Check if post type is public
        if ( ! FD_WebSocket_Push_Helper::is_public_post_type( $post->post_type ) ) {
            FD_WebSocket_Push_Helper::log( 'Skipping post status transition handler because post type "' . $post->post_type . '" is not public' );
            return;
        }
        
        // Determine event type and relevance
        $event_type = '';
        $is_relevant = false;
        
        if ( $old_status !== 'publish' && $new_status === 'publish' ) {
            // Post became published - equivalent to insertion
            $is_relevant = true;
            $event_type = 'post:status-published';
            FD_WebSocket_Push_Helper::log( 'Post status transition: article published (' . $old_status . ' => ' . $new_status . ')' );
        } elseif ( $old_status === 'publish' && $new_status !== 'publish' ) {
            // Post became unpublished - equivalent to deletion
            $is_relevant = true;
            $event_type = 'post:status-unpublished';
            FD_WebSocket_Push_Helper::log( 'Post status transition: article unpublished (' . $old_status . ' => ' . $new_status . ')' );
        } else {
            FD_WebSocket_Push_Helper::log( 'Post status transition not relevant for list updates: ' . $old_status . ' => ' . $new_status );
            return;
        }
        
        if ( ! $is_relevant ) {
            return;
        }
        
        FD_WebSocket_Push_Helper::log( 'Processing post status transition for post ' . $post->ID . ' (type: ' . $post->post_type . ', event: ' . $event_type . ')' );
        
        // Send WebSocket event
        $this->websocket_pusher->send_post_status_transition_event( $event_type, $post, $old_status, $new_status );
        
        // Invalidate caches
        $this->cache_invalidator->invalidate_caches_on_post_status_change( $post->ID, $post, $old_status, $new_status );
    }
}
