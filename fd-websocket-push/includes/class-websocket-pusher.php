<?php
/**
 * WebSocket push handler for FD WebSocket Push plugin
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class FD_WebSocket_Push_WebSocket_Pusher {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * WebSocket server URL
     */
    private $websocket_url = 'http://websocket:8080/push-event';
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Send event to WebSocket server
     *
     * @param string $event_type
     * @param string $target
     * @param array $data
     * @return bool|WP_Error
     */
    public function send_event( $event_type, $target, $data = [] ) {
        if ( ! FD_WebSocket_Push_Helper::is_websocket_push_enabled() ) {
            FD_WebSocket_Push_Helper::log( 'FD_WEBSOCKET_PUSH_SECRET not defined, cannot send event: ' . $event_type, 'ERROR' );
            return false;
        }
        
        $event_data = [
            'event'  => $event_type,
            'target' => $target,
            'data'   => $data,
        ];
        
        FD_WebSocket_Push_Helper::log( 'Sending WebSocket event: ' . $event_type . ' to target: ' . $target );
        
        $response = wp_remote_post( $this->websocket_url, [
            'method'   => 'POST',
            'headers'  => [
                'Content-Type'  => 'application/json; charset=utf-8',
                'x-push-secret' => FD_WebSocket_Push_Helper::get_websocket_push_secret(),
            ],
            'body'     => wp_json_encode( $event_data ),
            'blocking' => false,
        ] );
        
        if ( is_wp_error( $response ) ) {
            FD_WebSocket_Push_Helper::log( 'WebSocket push failed: ' . $response->get_error_message(), 'ERROR' );
            return $response;
        }
        
        FD_WebSocket_Push_Helper::log( 'WebSocket push sent successfully (non-blocking)' );
        return true;
    }
    
    /**
     * Send post updated event
     *
     * @param int $post_id
     * @param WP_Post $post
     */
    public function send_post_updated_event( $post_id, $post ) {
        $target_room = FD_WebSocket_Push_Helper::get_post_access_level( $post_id, $post );
        
        $data = [
            'postId'    => $post_id,
            'postType'  => $post->post_type,
            'status'    => $post->post_status,
            'shortUuid' => get_post_meta( $post_id, '_fd_short_uuid', true ),
            'slug'      => $post->post_name,
        ];
        
        return $this->send_event( 'post:updated', $target_room, $data );
    }
    
    /**
     * Send post updated for lists event
     *
     * @param int $post_id
     * @param WP_Post $post
     */
    public function send_post_updated_for_lists_event( $post_id, $post ) {
        // Get post categories
        $post_categories = wp_get_post_categories( $post_id );
        $categories = array_map( function( $cat_id ) {
            $category = get_category( $cat_id );
            return [
                'id'   => $cat_id,
                'slug' => $category->slug,
                'name' => $category->name,
            ];
        }, $post_categories );
        
        // Get post tags
        $post_tags = wp_get_post_tags( $post_id );
        $tags = array_map( function( $tag ) {
            return [
                'id'   => $tag->term_id,
                'slug' => $tag->slug,
                'name' => $tag->name,
            ];
        }, $post_tags );
        
        // Get custom taxonomies
        $custom_taxonomies_data = [];
        $custom_taxonomies = get_object_taxonomies( $post->post_type, 'objects' );
        foreach ( $custom_taxonomies as $taxonomy ) {
            if ( ! $taxonomy->public ) continue;
            
            $terms = wp_get_post_terms( $post_id, $taxonomy->name );
            if ( ! is_wp_error( $terms ) && ! empty( $terms ) ) {
                $custom_taxonomies_data[$taxonomy->name] = array_map( function( $term ) {
                    return [
                        'id'   => $term->term_id,
                        'slug' => $term->slug,
                        'name' => $term->name,
                    ];
                }, $terms );
            }
        }
        
        $data = [
            'postId'           => $post_id,
            'postType'         => $post->post_type,
            'status'           => $post->post_status,
            'shortUuid'        => get_post_meta( $post_id, '_fd_short_uuid', true ),
            'slug'             => $post->post_name,
            'title'            => $post->post_title,
            'excerpt'          => get_the_excerpt( $post ),
            'date'             => $post->post_date,
            'modified'         => $post->post_modified,
            'categories'       => $categories,
            'tags'             => $tags,
            'customTaxonomies' => $custom_taxonomies_data,
        ];
        
        return $this->send_event( 'post:updated-for-lists', 'public', $data );
    }
    
    /**
     * Send post inserted event
     *
     * @param int $post_id
     * @param WP_Post $post
     */
    public function send_post_inserted_event( $post_id, $post ) {
        $data = [
            'postId'    => $post_id,
            'postType'  => $post->post_type,
            'status'    => $post->post_status,
            'shortUuid' => get_post_meta( $post_id, '_fd_short_uuid', true ),
            'slug'      => $post->post_name,
            'title'     => $post->post_title,
            'excerpt'   => get_the_excerpt( $post ),
            'date'      => $post->post_date,
            'modified'  => $post->post_modified,
        ];
        
        return $this->send_event( 'post:inserted', 'public', $data );
    }
    
    /**
     * Send post deleted event
     *
     * @param int $post_id
     * @param WP_Post $post
     */
    public function send_post_deleted_event( $post_id, $post ) {
        $data = [
            'postId'    => $post_id,
            'postType'  => $post->post_type,
            'status'    => $post->post_status,
            'shortUuid' => get_post_meta( $post_id, '_fd_short_uuid', true ),
            'slug'      => $post->post_name,
            'title'     => $post->post_title,
        ];
        
        return $this->send_event( 'post:deleted', 'public', $data );
    }
    
    /**
     * Send post status transition event
     *
     * @param string $event_type
     * @param WP_Post $post
     * @param string $old_status
     * @param string $new_status
     */
    public function send_post_status_transition_event( $event_type, $post, $old_status, $new_status ) {
        $data = [
            'postId'     => $post->ID,
            'postType'   => $post->post_type,
            'oldStatus'  => $old_status,
            'newStatus'  => $new_status,
            'shortUuid'  => get_post_meta( $post->ID, '_fd_short_uuid', true ),
            'slug'       => $post->post_name,
            'title'      => $post->post_title,
            'excerpt'    => get_the_excerpt( $post ),
            'date'       => $post->post_date,
            'modified'   => $post->post_modified,
        ];
        
        return $this->send_event( $event_type, 'public', $data );
    }
    
    /**
     * Send taxonomy updated event
     *
     * @param string $event_type
     * @param int $term_id
     * @param string $taxonomy
     */
    public function send_taxonomy_updated_event( $event_type, $term_id, $taxonomy ) {
        $term = get_term( $term_id, $taxonomy );
        
        $data = [
            'termId'   => $term_id,
            'taxonomy' => $taxonomy,
            'slug'     => $term ? $term->slug : '',
            'name'     => $term ? $term->name : '',
        ];
        
        return $this->send_event( $event_type, 'public', $data );
    }
    
    /**
     * Send list item added/removed event
     *
     * @param string $event_type ('list:item-added' or 'list:item-removed')
     * @param int $post_id
     * @param array $affected_terms
     */
    public function send_list_item_event( $event_type, $post_id, $affected_terms ) {
        $data = [
            'postId'        => $post_id,
            'affectedTerms' => $affected_terms,
        ];
        
        return $this->send_event( $event_type, 'public', $data );
    }
    
    /**
     * Send post unlocked event
     *
     * @param int $user_id
     * @param int $post_id
     */
    public function send_post_unlocked_event( $user_id, $post_id ) {
        $data = [
            'postId' => (int) $post_id,
        ];
        
        return $this->send_event( 'post:unlocked', 'user_' . $user_id, $data );
    }
}
