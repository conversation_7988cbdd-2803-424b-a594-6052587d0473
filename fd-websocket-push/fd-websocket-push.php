<?php
/**
 * Plugin Name:       FD WebSocket Push
 * Description:       处理在特定 WordPress 事件发生时，向 WebSocket 服务器发送实时推送通知。
 * Version:           1.0.0
 * Author:            AI Assistant & Project Owner
 * Text Domain:       fd-websocket-push
 * Domain Path:       /languages
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Define plugin constants
define( 'FD_WEBSOCKET_PUSH_VERSION', '1.0.0' );
define( 'FD_WEBSOCKET_PUSH_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'FD_WEBSOCKET_PUSH_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'FD_WEBSOCKET_PUSH_INCLUDES_DIR', FD_WEBSOCKET_PUSH_PLUGIN_DIR . 'includes/' );

/**
 * Main plugin class
 */
class FD_WebSocket_Push {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize the plugin
     */
    private function init() {
        // Load required files
        $this->load_dependencies();
        
        // Initialize components
        $this->init_components();
        
        // Register hooks
        $this->register_hooks();
    }
    
    /**
     * Load required files
     */
    private function load_dependencies() {
        require_once FD_WEBSOCKET_PUSH_INCLUDES_DIR . 'class-helper.php';
        require_once FD_WEBSOCKET_PUSH_INCLUDES_DIR . 'class-cache-invalidator.php';
        require_once FD_WEBSOCKET_PUSH_INCLUDES_DIR . 'class-websocket-pusher.php';
        require_once FD_WEBSOCKET_PUSH_INCLUDES_DIR . 'class-post-event-handler.php';
        require_once FD_WEBSOCKET_PUSH_INCLUDES_DIR . 'class-taxonomy-event-handler.php';
        require_once FD_WEBSOCKET_PUSH_INCLUDES_DIR . 'class-payment-event-handler.php';
    }
    
    /**
     * Initialize components
     */
    private function init_components() {
        // Initialize all handler classes
        FD_WebSocket_Push_Post_Event_Handler::get_instance();
        FD_WebSocket_Push_Taxonomy_Event_Handler::get_instance();
        FD_WebSocket_Push_Payment_Event_Handler::get_instance();
    }
    
    /**
     * Register WordPress hooks
     */
    private function register_hooks() {
        // Plugin activation/deactivation hooks
        register_activation_hook( __FILE__, array( $this, 'activate' ) );
        register_deactivation_hook( __FILE__, array( $this, 'deactivate' ) );
        
        // Initialize after WordPress is fully loaded
        add_action( 'init', array( $this, 'late_init' ), 20 );
    }
    
    /**
     * Late initialization - runs after WordPress init
     */
    public function late_init() {
        // Register dynamic taxonomy hooks
        FD_WebSocket_Push_Taxonomy_Event_Handler::get_instance()->register_dynamic_taxonomy_hooks();
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Add any activation logic here
        error_log( 'FD WebSocket Push plugin activated' );
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Add any deactivation logic here
        error_log( 'FD WebSocket Push plugin deactivated' );
    }
}

/**
 * Initialize the plugin
 */
function fd_websocket_push_init() {
    return FD_WebSocket_Push::get_instance();
}

// Start the plugin
fd_websocket_push_init();

/**
 * Backward compatibility functions
 * These functions maintain compatibility with any external code that might call the old function names
 */

/**
 * Backward compatibility wrapper for revalidate tag function
 *
 * @param string $tag The tag to revalidate
 */
function fd_pusher_revalidate_tag( $tag ) {
    $cache_invalidator = FD_WebSocket_Push_Cache_Invalidator::get_instance();
    $cache_invalidator->revalidate_tag( $tag );
}

/**
 * Backward compatibility wrapper for revalidate term caches function
 *
 * @param WP_Term $term The term object
 */
function fd_pusher_revalidate_tag_for_term( $term ) {
    $cache_invalidator = FD_WebSocket_Push_Cache_Invalidator::get_instance();
    $cache_invalidator->revalidate_term_caches( $term );
}
